{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.AspNetCore.SpaProxy": "Information", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"InnoLogicielContext": "Host=localhost;Port=5555;Database=test1;Username=postgres;Password=******;"}, "ActivateAccountUrl": "https://localhost:7295/login", "Invoice": "https://localhost:7295", "ResetPasswordUrl": "https://localhost:7295/reset-password", "InviteUrlMember": "https://localhost:7295/activate-invite", "Host": "https://localhost:7295", "ENCRYPTION_KEY": "t79G3J4MjzqeW7dxywtpGH3q3YLsTJpKz2V8TR8IfIgpgqLBzpC6vBwdzu6XQC34", "Jwt": {"SecretKey": "t79G3J4MjzqeW7dxywtpGH3q3YLsTJpKz2V8TR8IfIgpgqLBzpC6vBwdzu6XQC34", "Issuer": "https://127.0.0.1:4300", "Audience": "https://127.0.0.1:4300"}, "DigitalOceanSpace": {"BucketName": "innobooks", "Key": "Wo2AbuDVtLrwLrlG1l2s", "Secret": "9V6Ihp8dRirHKJciOyW3xhs7gP2Mcwn8FSTrpqI1", "Endpoint": "https://minio.s3inno.com"}, "Stripe": {"ApiKey": "pk_test_51RKWmPP7k9JqKWGZmrvFrLjlJ3muJZ2zDgkvyyFfWsimbXglbRiJCkfNk9cIlhpPcdkUw6MXvhhYHjhI49xL5x3p00Cmliucjq", "ApiSecret": "sk_test_51RKWmPP7k9JqKWGZDCZM629nXocImunuiib4W2SIaaIIcAjGYPmeTXGs0gO0PW1rFv855tRBnxZa1Qbo485nQDmI00cULjbMXc", "WebhookSecret": "whsec_5424ab7f0ea31c100063fbab1b7bd9d263e25f297149e02358ab811708f0d755", "RedirectUrl": "https://localhost:7295/billing"}, "AllowedHosts": "*"}